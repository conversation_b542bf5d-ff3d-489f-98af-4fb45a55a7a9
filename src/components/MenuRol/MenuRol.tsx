import { FC, useState } from "react";
import TransferList from "../TransferList/TransferList";
import DropDown from "../MetlifeComponents/DropDown/DropDown";
import { DropDownOption } from "../MetlifeComponents/DropDown/DropDownOption";
import "./menuRol.css";

const MenuRol: FC = () => {
  // State for filters
  const [selectedModule, setSelectedModule] = useState<string>("I");
  const [selectedRole, setSelectedRole] = useState<string>("");

  // Dropdown options
  const moduleOptions: DropDownOption[] = [
    {
      ariaLabel: "I - Ingresos",
      label: "I - Ingresos",
      value: "I",
    },
    {
      ariaLabel: "E - Egresos",
      label: "E - Egresos",
      value: "E",
    },
  ];

  const roleOptions: DropDownOption[] = [
    {
      ariaLabel: "Administrador",
      label: "Administrador",
      value: "admin",
    },
    {
      ariaLabel: "<PERSON>aj<PERSON>",
      label: "<PERSON><PERSON><PERSON>",
      value: "cajero",
    },
    {
      ariaLabel: "Supervisor",
      label: "Supervisor",
      value: "supervisor",
    },
    {
      ariaLabel: "Contador",
      label: "Contador",
      value: "contador",
    },
    {
      ariaLabel: "Auditor",
      label: "Auditor",
      value: "auditor",
    },
  ];

  // Dropdown handlers
  const handleModuleChange = (value: string) => {
    console.log('🔄 Module changed to:', value);
    setSelectedModule(value);
  };

  const handleRoleChange = (value: string) => {
    console.log('👥 Role changed to:', value);
    setSelectedRole(value);
  };

  // Dropdown styles
  const dropdownStyle = {
    maxWidth: "250px",
    width: "100%",
    marginRight: "20px",
  };

  return (
    <div className="container">
      <h2 className="title">Menú Por Rol</h2>

      {/* Filter Controls */}
      <div className="filter-controls">
        <div className="form-inline">
          <label htmlFor="modulo">Módulo:</label>
          <DropDown
            change={handleModuleChange}
            click={() => {}}
            opts={moduleOptions}
            style={dropdownStyle}
            placeholder="Seleccione Módulo"
            disabled={false}
            selectedValue={selectedModule}
          />
        </div>
        <div className="form-inline">
          <label htmlFor="claveRol">Clave Rol:</label>
          <DropDown
            change={handleRoleChange}
            click={() => {}}
            opts={roleOptions}
            style={dropdownStyle}
            placeholder="Seleccione Rol"
            disabled={false}
            selectedValue={selectedRole}
          />
        </div>
      </div>

      <TransferList
        leftTitle="Menús Disponibles"
        rightTitle="Menús Asignados"
        selectedModule={selectedModule}
        selectedRole={selectedRole}
      />
    </div>
  );
};

export default MenuRol;