import { FC, useState, useEffect } from "react";
import "./transferList.css";

// Define the structure for tree items
interface TreeItem {
  id: string;
  name: string;
  module: string; // 'I' for Ingresos, 'E' for Egresos
  children?: TreeItem[];
}

// Props for the TransferList component
interface TransferListProps {
  leftTitle?: string;
  rightTitle?: string;
  selectedModule: string;
  selectedRole: string;
}

const TransferList: FC<TransferListProps> = ({
  leftTitle = "Lista Izquierda",
  rightTitle = "Lista Derecha",
  selectedModule,
  selectedRole,
}) => {
  // Sample initial data for left list with menu items
  const allMenuItems: TreeItem[] = [
    // INGRESOS
    {
      id: "1",
      name: "Ingresos - Catálogos",
      module: "I",
      children: [
        { id: "1-1", name: "Mantenimiento de Compañías", module: "I" },
        { id: "1-2", name: "Mantenimiento de Bancos", module: "I" },
        { id: "1-3", name: "Mantenimiento de Cuentas Bancarias", module: "I" },
        { id: "1-4", name: "Mantenimiento de Usuarios", module: "I" },
        { id: "1-5", name: "Mantenimiento de Roles", module: "I" },
        { id: "1-6", name: "Mantenimiento de Menús", module: "I" },
      ],
    },
    {
      id: "2",
      name: "Ingresos - Procesos",
      module: "I",
      children: [
        { id: "2-1", name: "Dotaciones a Cajeros", module: "I" },
        { id: "2-2", name: "Registro de Ingresos y Egresos Diarios", module: "I" },
        { id: "2-3", name: "Corte de Caja Normal", module: "I" },
        { id: "2-4", name: "Corte de Caja General", module: "I" },
        { id: "2-5", name: "Anulación de Operaciones", module: "I" },
        { id: "2-6", name: "Corte de Remesa", module: "I" },
      ],
    },
    {
      id: "3",
      name: "Ingresos - Consultas",
      module: "I",
      children: [
        { id: "3-1", name: "Consulta de Movimientos Históricos", module: "I" },
        { id: "3-2", name: "Monitoreo de Cajas", module: "I" },
        { id: "3-3", name: "Captura de Ingresos Pendientes", module: "I" },
      ],
    },
    {
      id: "4",
      name: "Ingresos - Reportes",
      module: "I",
      children: [
        { id: "4-1", name: "Comprobantes de Ingresos Históricos", module: "I" },
        { id: "4-2", name: "Reportes de Movimientos por Caja y Fecha", module: "I" },
        { id: "4-3", name: "Reporte de Movimientos de Ingresos por Área/Fecha", module: "I" },
      ],
    },
    // EGRESOS
    {
      id: "5",
      name: "Egresos - Catálogos",
      module: "E",
      children: [
        { id: "5-1", name: "Mantenimiento de Proveedores", module: "E" },
        { id: "5-2", name: "Mantenimiento de Conceptos de Pago", module: "E" },
        { id: "5-3", name: "Mantenimiento de Cuentas Contables", module: "E" },
        { id: "5-4", name: "Mantenimiento de Plantillas Contables", module: "E" },
        { id: "5-5", name: "Mantenimiento de Centros de Costo", module: "E" },
      ],
    },
    {
      id: "6",
      name: "Egresos - Procesos",
      module: "E",
      children: [
        { id: "6-1", name: "Autorización de Pagos", module: "E" },
        { id: "6-2", name: "Generación de Transferencias Bancarias", module: "E" },
        { id: "6-3", name: "Control de Cheques", module: "E" },
        { id: "6-4", name: "Registro de Gastos", module: "E" },
        { id: "6-5", name: "Conciliación Bancaria", module: "E" },
      ],
    },
    {
      id: "7",
      name: "Egresos - Consultas",
      module: "E",
      children: [
        { id: "7-1", name: "Consulta de Pagos Realizados", module: "E" },
        { id: "7-2", name: "Consulta de Saldos por Proveedor", module: "E" },
        { id: "7-3", name: "Consulta de Estados de Cuenta", module: "E" },
      ],
    },
    {
      id: "8",
      name: "Egresos - Reportes",
      module: "E",
      children: [
        { id: "8-1", name: "Reporte de Pagos por Período", module: "E" },
        { id: "8-2", name: "Reporte de Gastos por Centro de Costo", module: "E" },
        { id: "8-3", name: "Reporte de Conciliación Bancaria", module: "E" },
        { id: "8-4", name: "Reporte de Cheques Emitidos", module: "E" },
      ],
    },
  ];



// Function to get initial right items (moved outside component)
const getInitialRightItems = (module: string, role: string, sampleAssignments: { [key: string]: TreeItem[] }): TreeItem[] => {
  console.log('🔍 Getting initial right items for:', { module, role });
  console.log('📊 Available keys in sampleAssignments:', Object.keys(sampleAssignments));

  if (!role || role === "") {
    // Default items when no role is selected
    const defaultItems = module === "I" ? [
      {
        id: "default-i",
        name: "Menús Básicos de Ingresos",
        module: "I",
        children: [
          { id: "default-i-1", name: "Consulta de Movimientos", module: "I" },
          { id: "default-i-2", name: "Reportes Básicos", module: "I" },
        ],
      },
    ] : [
      {
        id: "default-e",
        name: "Menús Básicos de Egresos",
        module: "E",
        children: [
          { id: "default-e-1", name: "Consulta de Pagos", module: "E" },
          { id: "default-e-2", name: "Reportes Básicos", module: "E" },
        ],
      },
    ];
    console.log('✅ Returning default items for module:', module, defaultItems);
    return defaultItems;
  }

  const key = `${module}-${role}`;
  const result = sampleAssignments[key] || [];
  console.log('🔑 Key:', key, '📋 Result:', result);
  console.log('🎯 Result length:', result.length);
  if (result.length > 0) {
    console.log('📝 First item module:', result[0].module);
  }
  return result;
};

  // Sample assigned menus based on module and role
  const sampleAssignments: { [key: string]: TreeItem[] } = {
    // INGRESOS - ADMINISTRADOR
    'I-admin': [
      {
        id: "assigned-i-admin-1",
        name: "Administración Completa - Ingresos",
        module: "I",
        children: [
          { id: "assigned-i-admin-1-1", name: "Mantenimiento de Compañías", module: "I" },
          { id: "assigned-i-admin-1-2", name: "Mantenimiento de Usuarios", module: "I" },
          { id: "assigned-i-admin-1-3", name: "Mantenimiento de Roles", module: "I" },
          { id: "assigned-i-admin-1-4", name: "Mantenimiento de Menús", module: "I" },
        ],
      },
      {
        id: "assigned-i-admin-2",
        name: "Reportes Administrativos",
        module: "I",
        children: [
          { id: "assigned-i-admin-2-1", name: "Reporte de Movimientos de Ingresos por Área/Fecha", module: "I" },
          { id: "assigned-i-admin-2-2", name: "Reportes de Movimientos por Caja y Fecha", module: "I" },
        ],
      },
    ],
    // INGRESOS - CAJERO
    'I-cajero': [
      {
        id: "assigned-i-cajero-1",
        name: "Operaciones de Caja",
        module: "I",
        children: [
          { id: "assigned-i-cajero-1-1", name: "Registro de Ingresos y Egresos Diarios", module: "I" },
          { id: "assigned-i-cajero-1-2", name: "Corte de Caja Normal", module: "I" },
          { id: "assigned-i-cajero-1-3", name: "Captura de Ingresos Pendientes", module: "I" },
        ],
      },
      {
        id: "assigned-i-cajero-2",
        name: "Consultas Básicas",
        module: "I",
        children: [
          { id: "assigned-i-cajero-2-1", name: "Consulta de Movimientos Históricos", module: "I" },
          { id: "assigned-i-cajero-2-2", name: "Monitoreo de Cajas", module: "I" },
        ],
      },
    ],
    // INGRESOS - SUPERVISOR
    'I-supervisor': [
      {
        id: "assigned-i-supervisor-1",
        name: "Supervisión de Operaciones",
        module: "I",
        children: [
          { id: "assigned-i-supervisor-1-1", name: "Corte de Caja General", module: "I" },
          { id: "assigned-i-supervisor-1-2", name: "Anulación de Operaciones", module: "I" },
          { id: "assigned-i-supervisor-1-3", name: "Monitoreo de Cajas", module: "I" },
        ],
      },
      {
        id: "assigned-i-supervisor-2",
        name: "Reportes de Supervisión",
        module: "I",
        children: [
          { id: "assigned-i-supervisor-2-1", name: "Comprobantes de Ingresos Históricos", module: "I" },
        ],
      },
    ],
    // INGRESOS - CONTADOR
    'I-contador': [
      {
        id: "assigned-i-contador-1",
        name: "Contabilidad de Ingresos",
        module: "I",
        children: [
          { id: "assigned-i-contador-1-1", name: "Consulta de Movimientos Históricos", module: "I" },
          { id: "assigned-i-contador-1-2", name: "Corte de Remesa", module: "I" },
          { id: "assigned-i-contador-1-3", name: "Mantenimiento de Bancos", module: "I" },
        ],
      },
      {
        id: "assigned-i-contador-2",
        name: "Reportes Contables",
        module: "I",
        children: [
          { id: "assigned-i-contador-2-1", name: "Comprobantes de Ingresos Históricos", module: "I" },
          { id: "assigned-i-contador-2-2", name: "Reporte de Movimientos de Ingresos por Área/Fecha", module: "I" },
        ],
      },
    ],
    // INGRESOS - AUDITOR
    'I-auditor': [
      {
        id: "assigned-i-auditor-1",
        name: "Auditoría de Ingresos",
        module: "I",
        children: [
          { id: "assigned-i-auditor-1-1", name: "Consulta de Movimientos Históricos", module: "I" },
          { id: "assigned-i-auditor-1-2", name: "Comprobantes de Ingresos Históricos", module: "I" },
          { id: "assigned-i-auditor-1-3", name: "Reportes de Movimientos por Caja y Fecha", module: "I" },
        ],
      },
    ],
    // EGRESOS - ADMINISTRADOR
    'E-admin': [
      {
        id: "assigned-e-admin-1",
        name: "Administración Completa - Egresos",
        module: "E",
        children: [
          { id: "assigned-e-admin-1-1", name: "Mantenimiento de Proveedores", module: "E" },
          { id: "assigned-e-admin-1-2", name: "Mantenimiento de Conceptos de Pago", module: "E" },
          { id: "assigned-e-admin-1-3", name: "Mantenimiento de Centros de Costo", module: "E" },
        ],
      },
      {
        id: "assigned-e-admin-2",
        name: "Procesos Administrativos",
        module: "E",
        children: [
          { id: "assigned-e-admin-2-1", name: "Autorización de Pagos", module: "E" },
          { id: "assigned-e-admin-2-2", name: "Control de Cheques", module: "E" },
        ],
      },
    ],
    // EGRESOS - CAJERO
    'E-cajero': [
      {
        id: "assigned-e-cajero-1",
        name: "Operaciones Básicas de Egresos",
        module: "E",
        children: [
          { id: "assigned-e-cajero-1-1", name: "Registro de Gastos", module: "E" },
          { id: "assigned-e-cajero-1-2", name: "Consulta de Pagos Realizados", module: "E" },
          { id: "assigned-e-cajero-1-3", name: "Consulta de Estados de Cuenta", module: "E" },
        ],
      },
      {
        id: "assigned-e-cajero-2",
        name: "Mantenimiento Básico",
        module: "E",
        children: [
          { id: "assigned-e-cajero-2-1", name: "Mantenimiento de Proveedores", module: "E" },
        ],
      },
    ],
    // EGRESOS - SUPERVISOR
    'E-supervisor': [
      {
        id: "assigned-e-supervisor-1",
        name: "Supervisión de Egresos",
        module: "E",
        children: [
          { id: "assigned-e-supervisor-1-1", name: "Generación de Transferencias Bancarias", module: "E" },
          { id: "assigned-e-supervisor-1-2", name: "Consulta de Saldos por Proveedor", module: "E" },
          { id: "assigned-e-supervisor-1-3", name: "Reporte de Pagos por Período", module: "E" },
        ],
      },
      {
        id: "assigned-e-supervisor-2",
        name: "Autorizaciones",
        module: "E",
        children: [
          { id: "assigned-e-supervisor-2-1", name: "Autorización de Pagos", module: "E" },
          { id: "assigned-e-supervisor-2-2", name: "Control de Cheques", module: "E" },
        ],
      },
    ],
    // EGRESOS - CONTADOR
    'E-contador': [
      {
        id: "assigned-e-contador-1",
        name: "Contabilidad de Egresos",
        module: "E",
        children: [
          { id: "assigned-e-contador-1-1", name: "Mantenimiento de Cuentas Contables", module: "E" },
          { id: "assigned-e-contador-1-2", name: "Mantenimiento de Plantillas Contables", module: "E" },
          { id: "assigned-e-contador-1-3", name: "Conciliación Bancaria", module: "E" },
        ],
      },
      {
        id: "assigned-e-contador-2",
        name: "Reportes Contables",
        module: "E",
        children: [
          { id: "assigned-e-contador-2-1", name: "Reporte de Gastos por Centro de Costo", module: "E" },
          { id: "assigned-e-contador-2-2", name: "Reporte de Conciliación Bancaria", module: "E" },
        ],
      },
    ],
    // EGRESOS - AUDITOR
    'E-auditor': [
      {
        id: "assigned-e-auditor-1",
        name: "Auditoría de Egresos",
        module: "E",
        children: [
          { id: "assigned-e-auditor-1-1", name: "Consulta de Pagos Realizados", module: "E" },
          { id: "assigned-e-auditor-1-2", name: "Consulta de Estados de Cuenta", module: "E" },
          { id: "assigned-e-auditor-1-3", name: "Reporte de Cheques Emitidos", module: "E" },
        ],
      },
    ],
  };



  // State for both lists
  const [leftItems, setLeftItems] = useState<TreeItem[]>(allMenuItems); // Always show all items
  const [rightItems, setRightItems] = useState<TreeItem[]>(getInitialRightItems(selectedModule, selectedRole, sampleAssignments));

  // State for selected items
  const [selectedLeftItem, setSelectedLeftItem] = useState<{item: TreeItem | null, path: string[]}>({item: null, path: []});
  const [selectedRightItem, setSelectedRightItem] = useState<{item: TreeItem | null, path: string[]}>({item: null, path: []});

  // Effect to handle module and role changes (only affects right list)
  useEffect(() => {
    console.log('Filter changed:', { selectedModule, selectedRole });
    // Load assigned menus based on selected module and role
    const newRightItems = getInitialRightItems(selectedModule, selectedRole, sampleAssignments);
    console.log('New right items:', newRightItems);
    setRightItems(newRightItems);
    setSelectedLeftItem({item: null, path: []});
    setSelectedRightItem({item: null, path: []});
  }, [selectedModule, selectedRole]);

  // Helper function to remove an item by path
  const removeItemByPath = (items: TreeItem[], path: string[]): TreeItem[] => {
    if (path.length === 0) return items;

    const currentId = path[0];

    if (path.length === 1) {
      return items.filter(item => item.id !== currentId);
    }

    const itemIndex = items.findIndex(item => item.id === currentId);

    if (itemIndex === -1 || !items[itemIndex].children) return items;

    const updatedItems = [...items];
    updatedItems[itemIndex] = {
      ...updatedItems[itemIndex],
      children: removeItemByPath(updatedItems[itemIndex].children || [], path.slice(1))
    };

    return updatedItems;
  };

  // Helper function to add an item to a specific parent
  const addItemToParent = (items: TreeItem[], parentPath: string[], newItem: TreeItem): TreeItem[] => {
    if (parentPath.length === 0) {
      return [...items, newItem];
    }

    const currentId = parentPath[0];
    const itemIndex = items.findIndex(item => item.id === currentId);

    if (itemIndex === -1) return items;

    const updatedItems = [...items];

    if (parentPath.length === 1) {
      // Add to this parent's children
      updatedItems[itemIndex] = {
        ...updatedItems[itemIndex],
        children: [...(updatedItems[itemIndex].children || []), newItem]
      };
    } else {
      // Continue traversing
      updatedItems[itemIndex] = {
        ...updatedItems[itemIndex],
        children: addItemToParent(updatedItems[itemIndex].children || [], parentPath.slice(1), newItem)
      };
    }

    return updatedItems;
  };

  // Move selected item from left to right
  const moveSelectedToRight = () => {
    if (!selectedLeftItem.item) return;

    // Clone the item to move
    const itemToMove = { ...selectedLeftItem.item };

    // Remove from left list
    const newLeftItems = removeItemByPath(leftItems, selectedLeftItem.path);

    // Add to right list (either as root or as child of selected right item)
    let newRightItems;
    if (selectedRightItem.item) {
      newRightItems = addItemToParent(rightItems, selectedRightItem.path, itemToMove);
    } else {
      newRightItems = [...rightItems, itemToMove];
    }

    // Update state
    setLeftItems(newLeftItems);
    setRightItems(newRightItems);
    setSelectedLeftItem({item: null, path: []});
  };

  // Move selected item from right to left
  const moveSelectedToLeft = () => {
    if (!selectedRightItem.item) return;

    // Clone the item to move
    const itemToMove = { ...selectedRightItem.item };

    // Remove from right list
    const newRightItems = removeItemByPath(rightItems, selectedRightItem.path);

    // Add to left list (either as root or as child of selected left item)
    let newLeftItems;
    if (selectedLeftItem.item) {
      newLeftItems = addItemToParent(leftItems, selectedLeftItem.path, itemToMove);
    } else {
      newLeftItems = [...leftItems, itemToMove];
    }

    // Update state
    setRightItems(newRightItems);
    setLeftItems(newLeftItems);
    setSelectedRightItem({item: null, path: []});
  };

  // Move all items from left to right
  const moveAllToRight = () => {
    // If a right item is selected, add all left items as its children
    if (selectedRightItem.item) {
      let newRightItems = [...rightItems];

      // Add each left item as a child of the selected right item
      leftItems.forEach(item => {
        newRightItems = addItemToParent(newRightItems, selectedRightItem.path, { ...item });
      });

      setRightItems(newRightItems);
    } else {
      // Otherwise, just append all left items to the right list
      setRightItems([...rightItems, ...leftItems.map(item => ({ ...item }))]);
    }

    // Clear left list and reset to filtered items
    setLeftItems([]);
    setSelectedLeftItem({item: null, path: []});
  };

  // Move all items from right to left
  const moveAllToLeft = () => {
    // If a left item is selected, add all right items as its children
    if (selectedLeftItem.item) {
      let newLeftItems = [...leftItems];

      // Add each right item as a child of the selected left item
      rightItems.forEach(item => {
        newLeftItems = addItemToParent(newLeftItems, selectedLeftItem.path, { ...item });
      });

      setLeftItems(newLeftItems);
    } else {
      // Otherwise, just append all right items to the left list
      setLeftItems([...leftItems, ...rightItems.map(item => ({ ...item }))]);
    }

    // Clear right list
    setRightItems([]);
    setSelectedRightItem({item: null, path: []});
  };

  // Recursive function to render tree items
  const renderTreeItems = (
    items: TreeItem[],
    onSelect: (item: TreeItem, path: string[]) => void,
    selectedItem: {item: TreeItem | null, path: string[]},
    currentPath: string[] = []
  ) => {
    return (
      <ul className="transfer-list-items">
        {items.map((item) => {
          const itemPath = [...currentPath, item.id];
          const isSelected = selectedItem.item && selectedItem.path.join('-') === itemPath.join('-');

          return (
            <li
              key={item.id}
              className={`transfer-list-item ${isSelected ? 'selected' : ''}`}
              onClick={(e) => {
                e.stopPropagation();
                onSelect(item, itemPath);
              }}
            >
              <div className="item-content">
                <span>{item.name}</span>
              </div>

              {item.children && item.children.length > 0 && (
                renderTreeItems(item.children, onSelect, selectedItem, itemPath)
              )}
            </li>
          );
        })}
      </ul>
    );
  };

  return (
    <div className="transfer-list-wrapper">
      <div className="transfer-list-container">
        {/* Left List */}
        <div className="transfer-list-box">
          <h3 className="transfer-list-title">{leftTitle}</h3>
          <div className="transfer-list-content">
            {leftItems.length > 0 ? (
              renderTreeItems(
                leftItems,
                (item, path) => setSelectedLeftItem({item, path}),
                selectedLeftItem
              )
            ) : (
              <div className="empty-list">No hay elementos</div>
            )}
          </div>
        </div>

        {/* Transfer Buttons */}
        <div className="transfer-buttons">
          <button
            className="transfer-button"
            onClick={moveSelectedToRight}
            disabled={!selectedLeftItem.item}
            title="Mover elemento seleccionado a la derecha"
          >
            <span>&#8250;</span>
          </button>

          <button
            className="transfer-button"
            onClick={moveAllToRight}
            disabled={leftItems.length === 0}
            title="Mover todos los elementos a la derecha"
          >
            <span>&#187;</span>
          </button>

          <button
            className="transfer-button"
            onClick={moveSelectedToLeft}
            disabled={!selectedRightItem.item}
            title="Mover elemento seleccionado a la izquierda"
          >
            <span>&#8249;</span>
          </button>

          <button
            className="transfer-button"
            onClick={moveAllToLeft}
            disabled={rightItems.length === 0}
            title="Mover todos los elementos a la izquierda"
          >
            <span>&#171;</span>
          </button>
        </div>

        {/* Right List */}
        <div className="transfer-list-box">
          <h3 className="transfer-list-title">{rightTitle}</h3>
          <div className="transfer-list-content">
            {rightItems.length > 0 ? (
              renderTreeItems(
                rightItems,
                (item, path) => setSelectedRightItem({item, path}),
                selectedRightItem
              )
            ) : (
              <div className="empty-list">No hay elementos</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransferList;
