{"name": "14316-treasury-mx-portal-web", "version": "0.1.0", "private": true, "dependencies": {"@metlife/mldc-common": "^0.19.0", "@metlife/mldc-css": "^0.19.0", "@metlife/mldc-icons": "^0.19.0", "@metlife/mldc-react-button": "^0.20.0", "@metlife/mldc-react-checkbox": "^0.19.0", "@metlife/mldc-react-checkbox-group": "^0.19.0", "@metlife/mldc-react-data-table": "^0.19.0", "@metlife/mldc-react-dropdown": "^0.20.0", "@metlife/mldc-react-file-upload": "^0.19.0", "@metlife/mldc-react-hero-banner": "^0.19.0", "@metlife/mldc-react-input-number": "^0.19.0", "@metlife/mldc-react-input-text": "^0.19.0", "@metlife/mldc-react-left-nav": "^0.19.0", "@metlife/mldc-react-loading-indicator": "^0.20.0", "@metlife/mldc-react-radio": "^0.19.0", "@metlife/mldc-react-slide-panel": "^0.19.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "prettier": "^3.5.3", "react": "18.0.0", "react-bootstrap-icons": "^1.11.5", "react-dom": "18.0.0", "react-router-dom": "^7.5.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "set PORT=5173 && react-scripts start", "build": "npm version patch --no-git-tag-version && react-build-info && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "serve": "react-scripts start"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-build-info": "^0.0.3"}}